"use client";

import { useState } from "react";
import Image from "next/image";
import Link from "next/link";
import { useTranslations } from "next-intl";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@/components/ui/table";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuLabel,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";
import {
	MoreHorizontal,
	Search,
	Edit,
	Trash,
	CheckCircle2,
	XCircle,
} from "lucide-react";

// Mock data for clinics
const mockClinics = [
	{
		id: "1",
		name: "Central Pet Clinic",
		email: "<EMAIL>",
		imageUrl: "https://placehold.co/100x100/e2e8f0/94a3b8?text=Clinic+1",
		address: "123 Main St",
		city: "New York",
		state: "NY",
		phone: "(*************",
		website: "www.centralclinic.com",
		joinedDate: "2023-01-15",
		verified: true,
	},
	{
		id: "2",
		name: "Paws & Claws Veterinary",
		email: "<EMAIL>",
		imageUrl: "https://placehold.co/100x100/e2e8f0/94a3b8?text=Clinic+2",
		address: "456 Oak Ave",
		city: "Boston",
		state: "MA",
		phone: "(*************",
		website: "www.pawsclaws.com",
		joinedDate: "2023-02-10",
		verified: true,
	},
	{
		id: "3",
		name: "Whiskers Animal Hospital",
		email: "<EMAIL>",
		imageUrl: "https://placehold.co/100x100/e2e8f0/94a3b8?text=Clinic+3",
		address: "789 Pine Blvd",
		city: "Chicago",
		state: "IL",
		phone: "(*************",
		website: "www.whiskershospital.com",
		joinedDate: "2023-03-05",
		verified: false,
	},
	{
		id: "4",
		name: "Feline Friends Clinic",
		email: "<EMAIL>",
		imageUrl: "https://placehold.co/100x100/e2e8f0/94a3b8?text=Clinic+4",
		address: "321 Cedar St",
		city: "Los Angeles",
		state: "CA",
		phone: "(*************",
		website: "www.felinefriends.org",
		joinedDate: "2023-04-20",
		verified: true,
	},
	{
		id: "5",
		name: "Happy Tails Veterinary",
		email: "<EMAIL>",
		imageUrl: "https://placehold.co/100x100/e2e8f0/94a3b8?text=Clinic+5",
		address: "654 Birch Rd",
		city: "Seattle",
		state: "WA",
		phone: "(*************",
		website: "www.happytails.vet",
		joinedDate: "2023-05-25",
		verified: false,
	},
];

export function AdminClinicsList() {
	const t = useTranslations("admin.clinics");
	const [searchQuery, setSearchQuery] = useState("");
	const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
	const [clinicToDelete, setClinicToDelete] = useState<string | null>(null);
	const { toast } = useToast();

	const filteredClinics = mockClinics.filter(
		(clinic) =>
			clinic.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
			clinic.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
			clinic.city.toLowerCase().includes(searchQuery.toLowerCase()) ||
			clinic.state.toLowerCase().includes(searchQuery.toLowerCase())
	);

	const handleVerificationChange = (clinicId: string, verified: boolean) => {
		toast({
			title: verified ? "Clinic verified" : "Verification removed",
			description: `Clinic #${clinicId} verification status updated`,
		});
	};

	const handleDeleteClick = (clinicId: string) => {
		setClinicToDelete(clinicId);
		setDeleteDialogOpen(true);
	};

	const confirmDelete = () => {
		if (clinicToDelete) {
			toast({
				title: "Clinic deleted",
				description: `Clinic #${clinicToDelete} has been deleted`,
			});
			setClinicToDelete(null);
			setDeleteDialogOpen(false);
		}
	};

	return (
		<div className="space-y-4">
			<div className="flex items-center">
				<div className="relative flex-1 max-w-sm">
					<Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
					<Input
						type="search"
						placeholder={t("searchPlaceholder")}
						className="pl-8"
						value={searchQuery}
						onChange={(e) => setSearchQuery(e.target.value)}
					/>
				</div>
			</div>

			<div className="border rounded-md">
				<Table>
					<TableHeader>
						<TableRow>
							<TableHead>{t("columns.clinic")}</TableHead>
							<TableHead>{t("columns.location")}</TableHead>
							<TableHead>{t("columns.contact")}</TableHead>
							<TableHead>{t("columns.status")}</TableHead>
							<TableHead className="w-[100px]">
								{t("table.actions", { ns: "admin" })}
							</TableHead>
						</TableRow>
					</TableHeader>
					<TableBody>
						{filteredClinics.map((clinic) => (
							<TableRow key={clinic.id}>
								<TableCell>
									<div className="flex items-center space-x-3">
										<div className="relative h-10 w-10 rounded-md overflow-hidden bg-muted">
											<Image
												src={
													clinic.imageUrl ||
													"https://placehold.co/100x100/e2e8f0/94a3b8?text=Clinic"
												}
												alt={clinic.name}
												fill
												className="object-cover"
												onError={(e) => {
													e.currentTarget.src =
														"https://placehold.co/100x100/e2e8f0/94a3b8?text=Clinic";
												}}
											/>
										</div>
										<div>
											<div className="font-medium">
												{clinic.name}
											</div>
											<div className="text-sm text-muted-foreground">
												{clinic.email}
											</div>
										</div>
									</div>
								</TableCell>
								<TableCell>
									<div className="text-sm">
										<div>{clinic.address}</div>
										<div>
											{clinic.city}, {clinic.state}
										</div>
									</div>
								</TableCell>
								<TableCell>
									<div className="text-sm">
										<div>{clinic.phone}</div>
										<div className="text-muted-foreground">
											{clinic.website}
										</div>
									</div>
								</TableCell>
								<TableCell>
									{clinic.verified ? (
										<Badge
											variant="outline"
											className="border-green-500 text-green-500"
										>
											<CheckCircle2 className="h-3.5 w-3.5 mr-1" />
											{t("status.verified")}
										</Badge>
									) : (
										<Badge
											variant="outline"
											className="border-amber-500 text-amber-500"
										>
											{t("status.unverified")}
										</Badge>
									)}
								</TableCell>
								<TableCell>
									<DropdownMenu>
										<DropdownMenuTrigger asChild>
											<Button variant="ghost" size="icon">
												<MoreHorizontal className="h-4 w-4" />
												<span className="sr-only">
													{t("table.actions", {
														ns: "admin",
													})}
												</span>
											</Button>
										</DropdownMenuTrigger>
										<DropdownMenuContent align="end">
											<DropdownMenuLabel>
												{t("table.actions", {
													ns: "admin",
												})}
											</DropdownMenuLabel>
											<DropdownMenuSeparator />
											<DropdownMenuItem asChild>
												<Link
													href={`/clinics/${clinic.id}`}
												>
													{t("actions.viewProfile")}
												</Link>
											</DropdownMenuItem>
											<DropdownMenuItem asChild>
												<Link
													href={`/admin/clinics/${clinic.id}/edit`}
												>
													<Edit className="h-4 w-4 mr-2" />
													{t("actions.edit")}
												</Link>
											</DropdownMenuItem>
											<DropdownMenuSeparator />
											{clinic.verified ? (
												<DropdownMenuItem
													onClick={() =>
														handleVerificationChange(
															clinic.id,
															false
														)
													}
												>
													<XCircle className="h-4 w-4 mr-2 text-amber-500" />
													{t("actions.unverify")}
												</DropdownMenuItem>
											) : (
												<DropdownMenuItem
													onClick={() =>
														handleVerificationChange(
															clinic.id,
															true
														)
													}
												>
													<CheckCircle2 className="h-4 w-4 mr-2 text-green-500" />
													{t("actions.verify")}
												</DropdownMenuItem>
											)}
											<DropdownMenuSeparator />
											<DropdownMenuItem
												onClick={() =>
													handleDeleteClick(clinic.id)
												}
												className="text-red-500"
											>
												<Trash className="h-4 w-4 mr-2" />
												{t("actions.delete")}
											</DropdownMenuItem>
										</DropdownMenuContent>
									</DropdownMenu>
								</TableCell>
							</TableRow>
						))}

						{filteredClinics.length === 0 && (
							<TableRow>
								<TableCell
									colSpan={5}
									className="text-center py-6 text-muted-foreground"
								>
									No clinics found
								</TableCell>
							</TableRow>
						)}
					</TableBody>
				</Table>
			</div>

			<Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
				<DialogContent>
					<DialogHeader>
						<DialogTitle>Confirm Deletion</DialogTitle>
						<DialogDescription>
							Are you sure you want to delete this clinic? This
							action cannot be undone.
						</DialogDescription>
					</DialogHeader>
					<DialogFooter>
						<Button
							variant="outline"
							onClick={() => setDeleteDialogOpen(false)}
						>
							Cancel
						</Button>
						<Button variant="destructive" onClick={confirmDelete}>
							Delete
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>
		</div>
	);
}
