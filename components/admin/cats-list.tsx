"use client";

import { useState } from "react";
import { useTranslations } from "next-intl";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from "@/components/ui/dialog";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { Trash, Edit, Eye } from "lucide-react";
import {
	DataTable,
	type Column,
	type FilterOption,
	type Action,
} from "./data-table";
import { StatusBadge } from "./status-badge";
import { api } from "@/lib/trpc/react";

interface Cat {
	id: number;
	name: string;
	slug: string;
	gender: "male" | "female";
	age: number;
	status: "available" | "pending" | "adopted" | "unavailable";
	createdAt: Date;
	user: {
		id: number;
		name: string;
		email: string;
		role: string;
	};
	images: Array<{
		id: number;
		url: string;
		isPrimary: boolean | null;
		catId: number;
		createdAt: Date;
	}>;
	wilaya?: { name: string } | null;
	commune?: { name: string } | null;
}

interface AdminCatsListProps {
	limit?: number;
}

export function AdminCatsList({ limit }: AdminCatsListProps) {
	const t = useTranslations("admin");
	const { toast } = useToast();

	// State for table controls
	const [search, setSearch] = useState("");
	const [statusFilter, setStatusFilter] = useState<
		"available" | "pending" | "adopted" | "unavailable" | ""
	>("");
	const [sortBy, setSortBy] = useState<"name" | "createdAt" | "updatedAt">(
		"createdAt"
	);
	const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");
	const [page, setPage] = useState(0);
	const pageSize = limit || 20;

	// State for dialogs
	const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
	const [statusDialogOpen, setStatusDialogOpen] = useState(false);
	const [selectedCat, setSelectedCat] = useState<Cat | null>(null);
	const [newStatus, setNewStatus] = useState<
		"available" | "pending" | "adopted" | "unavailable"
	>("available");

	// Fetch cats data
	const {
		data: catsData,
		isLoading,
		refetch,
	} = api.admin.listCats.useQuery({
		limit: pageSize,
		offset: page * pageSize,
		search: search || undefined,
		status: statusFilter || undefined,
		sortBy,
		sortOrder,
	});

	// Mutations
	const updateCatStatusMutation = api.admin.updateCatStatus.useMutation({
		onSuccess: () => {
			toast({
				title: t("cats.statusUpdated"),
				description: t("cats.statusUpdatedDescription"),
			});
			refetch();
			setStatusDialogOpen(false);
			setSelectedCat(null);
		},
		onError: (error) => {
			toast({
				title: t("common.error"),
				description: error.message,
				variant: "destructive",
			});
		},
	});

	const deleteCatMutation = api.admin.deleteCat.useMutation({
		onSuccess: () => {
			toast({
				title: t("cats.deleted"),
				description: t("cats.deletedDescription"),
			});
			refetch();
			setDeleteDialogOpen(false);
			setSelectedCat(null);
		},
		onError: (error) => {
			toast({
				title: t("common.error"),
				description: error.message,
				variant: "destructive",
			});
		},
	});

	const cats = catsData?.cats || [];

	// Table columns
	const columns: Column<Cat>[] = [
		{
			key: "cat",
			label: t("cats.columns.cat"),
			render: (cat) => (
				<div className="flex items-center space-x-3">
					<div className="relative h-10 w-10 rounded-md overflow-hidden bg-muted">
						{cat.images[0] ? (
							<Image
								src={cat.images[0].url}
								alt={cat.name}
								fill
								className="object-cover"
							/>
						) : (
							<div className="w-full h-full bg-muted flex items-center justify-center text-xs text-muted-foreground">
								No Image
							</div>
						)}
					</div>
					<div>
						<div className="font-medium">{cat.name}</div>
						<div className="text-sm text-muted-foreground">
							{cat.gender} • {cat.age}{" "}
							{t("cats.columns.yearsOld")}
						</div>
					</div>
				</div>
			),
		},
		{
			key: "owner",
			label: t("cats.columns.owner"),
			render: (cat) => (
				<div>
					<div className="font-medium">{cat.user.name}</div>
					<div className="text-sm text-muted-foreground">
						{cat.user.email}
					</div>
				</div>
			),
		},
		{
			key: "location",
			label: t("cats.columns.location"),
			render: (cat) => (
				<div className="text-sm">
					{cat.wilaya?.name && cat.commune?.name
						? `${cat.commune.name}, ${cat.wilaya.name}`
						: cat.wilaya?.name || "N/A"}
				</div>
			),
		},
		{
			key: "status",
			label: t("cats.columns.status"),
			render: (cat) => <StatusBadge status={cat.status} type="cat" />,
		},
		{
			key: "createdAt",
			label: t("cats.columns.createdAt"),
			render: (cat) => (
				<div className="text-sm">
					{new Date(cat.createdAt).toLocaleDateString()}
				</div>
			),
		},
	];

	// Table filters
	const filters: FilterOption[] = [
		{
			key: "status",
			label: t("cats.filters.status"),
			options: [
				{ value: "", label: t("cats.filters.allStatuses") },
				{ value: "available", label: t("cats.status.available") },
				{ value: "pending", label: t("cats.status.pending") },
				{ value: "adopted", label: t("cats.status.adopted") },
				{ value: "unavailable", label: t("cats.status.unavailable") },
			],
		},
	];

	// Table actions
	const actions: Action<Cat>[] = [
		{
			label: t("cats.actions.view"),
			icon: <Eye className="h-4 w-4" />,
			onClick: (cat) => {
				window.open(`/cats/${cat.slug}`, "_blank");
			},
		},
		{
			label: t("cats.actions.changeStatus"),
			icon: <Edit className="h-4 w-4" />,
			onClick: (cat) => {
				setSelectedCat(cat);
				setNewStatus(cat.status);
				setStatusDialogOpen(true);
			},
		},
		{
			label: t("cats.actions.delete"),
			icon: <Trash className="h-4 w-4" />,
			onClick: (cat) => {
				setSelectedCat(cat);
				setDeleteDialogOpen(true);
			},
			variant: "destructive",
		},
	];

	const handleStatusUpdate = () => {
		if (!selectedCat) return;

		updateCatStatusMutation.mutate({
			catId: selectedCat.id,
			status: newStatus,
		});
	};

	const handleDelete = () => {
		if (!selectedCat) return;

		deleteCatMutation.mutate({
			catId: selectedCat.id,
		});
	};

	return (
		<div className="space-y-4">
			<DataTable
				data={cats}
				columns={columns}
				loading={isLoading}
				searchPlaceholder={t("cats.searchPlaceholder")}
				searchValue={search}
				onSearchChange={setSearch}
				filters={filters}
				filterValues={{ status: statusFilter }}
				onFilterChange={(key, value) => {
					if (key === "status") {
						setStatusFilter(
							value as
								| "available"
								| "pending"
								| "adopted"
								| "unavailable"
								| ""
						);
					}
				}}
				sortBy={sortBy}
				sortOrder={sortOrder}
				onSortChange={(key, order) => {
					setSortBy(key as any);
					setSortOrder(order);
				}}
				actions={actions}
				pagination={
					catsData
						? {
								total: catsData.total,
								limit: pageSize,
								offset: page * pageSize,
								onPageChange: (offset) =>
									setPage(offset / pageSize),
							}
						: undefined
				}
				emptyMessage={t("cats.noCats")}
			/>

			{/* Status Update Dialog */}
			<Dialog open={statusDialogOpen} onOpenChange={setStatusDialogOpen}>
				<DialogContent>
					<DialogHeader>
						<DialogTitle>{t("cats.changeStatus")}</DialogTitle>
						<DialogDescription>
							{t("cats.changeStatusDescription", {
								name: selectedCat?.name || "",
							})}
						</DialogDescription>
					</DialogHeader>
					<div className="py-4">
						<Select
							value={newStatus}
							onValueChange={(value) =>
								setNewStatus(
									value as
										| "available"
										| "pending"
										| "adopted"
										| "unavailable"
								)
							}
						>
							<SelectTrigger>
								<SelectValue />
							</SelectTrigger>
							<SelectContent>
								<SelectItem value="available">
									{t("cats.status.available")}
								</SelectItem>
								<SelectItem value="pending">
									{t("cats.status.pending")}
								</SelectItem>
								<SelectItem value="adopted">
									{t("cats.status.adopted")}
								</SelectItem>
								<SelectItem value="unavailable">
									{t("cats.status.unavailable")}
								</SelectItem>
							</SelectContent>
						</Select>
					</div>
					<DialogFooter>
						<Button
							variant="outline"
							onClick={() => setStatusDialogOpen(false)}
						>
							{t("common.cancel")}
						</Button>
						<Button
							onClick={handleStatusUpdate}
							disabled={updateCatStatusMutation.isPending}
						>
							{updateCatStatusMutation.isPending
								? t("common.updating")
								: t("common.update")}
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>

			{/* Delete Confirmation Dialog */}
			<Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
				<DialogContent>
					<DialogHeader>
						<DialogTitle>{t("cats.deleteCat")}</DialogTitle>
						<DialogDescription>
							{t("cats.deleteCatDescription", {
								name: selectedCat?.name || "",
							})}
						</DialogDescription>
					</DialogHeader>
					<DialogFooter>
						<Button
							variant="outline"
							onClick={() => setDeleteDialogOpen(false)}
						>
							{t("common.cancel")}
						</Button>
						<Button
							variant="destructive"
							onClick={handleDelete}
							disabled={deleteCatMutation.isPending}
						>
							{deleteCatMutation.isPending
								? t("common.deleting")
								: t("common.delete")}
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>
		</div>
	);
}
